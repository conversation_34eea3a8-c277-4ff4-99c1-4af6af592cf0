{"name": "app_rainbowpaws", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:production": "next build && node scripts/copy-uploads.js", "build:simple": "next build", "start": "next start", "production": "npm run build:production && next start", "start:any-port": "node -e \"const port = process.argv[2] || 3001; console.log('Starting on port ' + port); require('child_process').spawn('npx', ['next', 'start', '-p', port], {stdio: 'inherit'});\"", "lint": "next lint --max-warnings=0", "lint:fix": "next lint --fix", "type-check": "tsc --pretty --noEmit", "type-check:watch": "tsc --noEmit --watch --pretty", "check": "npm run type-check && npm run lint", "check:fix": "npm run lint:fix && npm run type-check"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "emailjs": "^4.0.3", "framer-motion": "^12.10.4", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "mysql2": "^3.14.1", "next": "15.3.2", "next-auth": "^4.24.11", "node-fetch": "^2.7.0", "nodemailer": "^7.0.3", "react": "^19", "react-dom": "^19", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0", "tailwind-merge": "^3.3.0", "twilio": "^5.6.1"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.2", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.17", "@types/node": "22.15.17", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.4", "@types/react-dom": "^19.1.5", "@types/twilio": "^3.19.2", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}}